import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from './components/layout/Layout'
import { ProtectedRoute } from './components/ProtectedRoute'
import { LoginPage } from './pages/auth/LoginPage'
import { DashboardPage } from './pages/DashboardPage'
import { ChatPage } from './pages/ChatPage'
import { useAuthStore } from './store/authStore'
import type { UserRole } from './types'

function App() {
  const { isAuthenticated } = useAuthStore()
const requiredRoles = ['admin', 'lawyer', 'assistant'] satisfies UserRole[];
  return (
    <Router>
      <Routes>
        {/* Routes publiques */}
        <Route
          path="/login"
          element={
            isAuthenticated ? <Navigate to="/dashboard" replace /> : <LoginPage />
          }
        />

        {/* Routes protégées */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />

          <Route
            path="dashboard"
            element={
              <ProtectedRoute requiredRoles={requiredRoles}>
                <DashboardPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="chat"
            element={
              <ProtectedRoute>
                <ChatPage />
              </ProtectedRoute>
            }
          />

          {/* Placeholder pour les autres pages */}
          <Route path="search" element={<div className="p-6"><h1 className="text-2xl font-bold">Recherche Juridique</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="document-analysis" element={<div className="p-6"><h1 className="text-2xl font-bold">Analyse de Documents</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="document-generator" element={<div className="p-6"><h1 className="text-2xl font-bold">Générateur de Documents</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="clients" element={<div className="p-6"><h1 className="text-2xl font-bold">Gestion des Clients</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="cases" element={<div className="p-6"><h1 className="text-2xl font-bold">Gestion des Dossiers</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="calendar" element={<div className="p-6"><h1 className="text-2xl font-bold">Agenda</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
          <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Paramètres</h1><p className="text-gray-600 mt-2">Cette page sera développée prochainement.</p></div>} />
        </Route>

        {/* Page d'erreur pour les routes non autorisées */}
        <Route
          path="/unauthorized"
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">403</h1>
                <p className="text-xl text-gray-600 mb-8">Accès non autorisé</p>
                <p className="text-gray-500">Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>
              </div>
            </div>
          }
        />

        {/* Page 404 */}
        <Route
          path="*"
          element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-xl text-gray-600 mb-8">Page non trouvée</p>
                <p className="text-gray-500">La page que vous recherchez n'existe pas.</p>
              </div>
            </div>
          }
        />
      </Routes>
    </Router>
  )
}

export default App
