// Types pour l'authentification et les utilisateurs
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export type UserRole = 'admin' | 'lawyer' | 'assistant' | 'client';

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Types pour les clients
export interface Client {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  address?: string
  company?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
}

// Types pour les dossiers
export interface CaseFile {
  id: string
  title: string
  description: string
  clientId: string
  client?: Client
  status: CaseStatus
  priority: CasePriority
  assignedLawyerId: string
  assignedLawyer?: User
  documents: Document[]
  notes: Note[]
  deadlines: Deadline[]
  createdAt: Date
  updatedAt: Date
}

export type CaseStatus = 'draft'|'active'| 'on_hold'| 'closed'|'archived';

export type CasePriority = 'low' | 'medium' | 'high' | 'urgent';

// Types pour les documents
export interface Document {
  id: string
  name: string
  type: DocumentType
  size: number
  url: string
  caseFileId?: string
  uploadedBy: string
  uploadedAt: Date
  tags: string[]
}

export type DocumentType = 'contract' | 'letter' | 'court_document' | 'evidence' | 'correspondence' | 'other';

// Types pour les notes
export interface Note {
  id: string
  content: string
  caseFileId: string
  authorId: string
  author?: User
  createdAt: Date
  updatedAt: Date
}

// Types pour les échéances
export interface Deadline {
  id: string
  title: string
  description?: string
  dueDate: Date
  caseFileId: string
  assignedTo: string
  completed: boolean
  createdAt: Date
  updatedAt: Date
}

// Types pour la recherche juridique
export interface LegalSearchQuery {
  query: string
  domain?: LegalDomain
  jurisdiction?: string
  dateRange?: {
    from: Date
    to: Date
  }
}

export interface LegalSearchResult {
  id: string
  title: string
  content: string
  source: LegalSource
  relevanceScore: number
  url?: string
  date?: Date
  citations: string[]
}

export type LegalDomain = 'civil' | 'criminal' | 'commercial' | 'labor' | 'administrative' | 'tax' | 'family' | 'real_estate';


export interface LegalSource {
  type: SourceType
  name: string
  reference: string
}

export type SourceType = 'law' | 'jurisprudence' | 'doctrine' | 'regulation';


// Types pour le chat IA
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  attachments?: ChatAttachment[]
}

export interface ChatAttachment {
  id: string
  name: string
  type: string
  url: string
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  createdAt: Date
  updatedAt: Date
}

// Types pour la génération de documents
export interface DocumentTemplate {
  id: string
  name: string
  description: string
  category: DocumentCategory
  template: string
  fields: TemplateField[]
  createdAt: Date
  updatedAt: Date
}

export type DocumentCategory = 'contract' | 'letter' | 'legal_notice' | 'court_filing' | 'agreement';

export interface TemplateField {
  id: string
  name: string
  label: string
  type: FieldType
  required: boolean
  defaultValue?: string
  options?: string[]
}

export type FieldType = 'text' | 'textarea' | 'date' | 'number' | 'select' | 'checkbox';

export interface GeneratedDocument {
  id: string
  templateId: string
  template?: DocumentTemplate
  content: string
  data: Record<string, any>
  generatedAt: Date
  generatedBy: string
}

// Types pour l'analyse de documents
export interface DocumentAnalysis {
  id: string
  documentId: string
  document?: Document
  summary: string
  keyPoints: string[]
  risks: Risk[]
  suggestions: string[]
  confidence: number
  analyzedAt: Date
}

export interface Risk {
  level: RiskLevel
  description: string
  recommendation: string
}

export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// Types pour les API responses
export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Types pour les erreurs
export interface ApiError {
  message: string
  code: string
  details?: any
}
