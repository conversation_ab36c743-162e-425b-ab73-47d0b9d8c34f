import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, AuthState, UserRole} from '../types/index'

interface AuthStore extends AuthState {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  register: (userData: RegisterData) => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<void>
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true })
        try {
          // Simulation d'un appel API
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Mock user data - à remplacer par un vrai appel API
          const mockUser: User = {
            id: '1',
            email,
            firstName: '<PERSON>',
            lastName: 'Doe',
            role: 'lawyer',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
            createdAt: new Date(),
            updatedAt: new Date()
          }

          set({
            user: mockUser,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false
        })
      },

      register: async (userData: RegisterData) => {
        set({ isLoading: true })
        try {
          // Simulation d'un appel API
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          const newUser: User = {
            id: Math.random().toString(36).substr(2, 9),
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            role: userData.role,
            createdAt: new Date(),
            updatedAt: new Date()
          }

          set({
            user: newUser,
            isAuthenticated: true,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      updateProfile: async (userData: Partial<User>) => {
        const { user } = get()
        if (!user) return

        set({ isLoading: true })
        try {
          // Simulation d'un appel API
          await new Promise(resolve => setTimeout(resolve, 500))
          
          const updatedUser = {
            ...user,
            ...userData,
            updatedAt: new Date()
          }

          set({
            user: updatedUser,
            isLoading: false
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      setUser: (user: User | null) => {
        set({
          user,
          isAuthenticated: !!user
        })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
